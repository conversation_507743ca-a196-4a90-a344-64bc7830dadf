/* TipTap Editor Styles */
.tiptap-editor-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.editor-wrapper {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
  background: #fff;
}

.editor-content {
  height: 100%;
  min-height: 500px;
}

/* ProseMirror Editor Styles */
.ProseMirror {
  outline: none;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  font-size: 16px;
  line-height: 1.6;
  color: #333;
  max-width: none;
  padding: 0;
}

.ProseMirror p {
  margin: 0 0 16px 0;
}

.ProseMirror p:last-child {
  margin-bottom: 0;
}

.ProseMirror h1 {
  font-size: 2.2em;
  font-weight: 700;
  margin: 32px 0 16px 0;
  color: #1a1a1a;
  border-bottom: 2px solid #ee1d1d;
  padding-bottom: 8px;
}

.ProseMirror h2 {
  font-size: 1.8em;
  font-weight: 600;
  margin: 28px 0 14px 0;
  color: #2a2a2a;
}

.ProseMirror h3 {
  font-size: 1.5em;
  font-weight: 600;
  margin: 24px 0 12px 0;
  color: #3a3a3a;
}

.ProseMirror h4 {
  font-size: 1.3em;
  font-weight: 600;
  margin: 20px 0 10px 0;
  color: #4a4a4a;
}

.ProseMirror h5 {
  font-size: 1.1em;
  font-weight: 600;
  margin: 18px 0 8px 0;
  color: #5a5a5a;
}

.ProseMirror h6 {
  font-size: 1em;
  font-weight: 600;
  margin: 16px 0 6px 0;
  color: #6a6a6a;
}

/* CSS计数器用于标题编号 */
.ProseMirror {
  counter-reset: h1-counter;
}

.ProseMirror .numbered-heading[data-level="1"] {
  counter-increment: h1-counter;
  counter-reset: h2-counter h3-counter h4-counter h5-counter h6-counter;
}

.ProseMirror .numbered-heading[data-level="2"] {
  counter-increment: h2-counter;
  counter-reset: h3-counter h4-counter h5-counter h6-counter;
}

.ProseMirror .numbered-heading[data-level="3"] {
  counter-increment: h3-counter;
  counter-reset: h4-counter h5-counter h6-counter;
}

.ProseMirror .numbered-heading[data-level="4"] {
  counter-increment: h4-counter;
  counter-reset: h5-counter h6-counter;
}

.ProseMirror .numbered-heading[data-level="5"] {
  counter-increment: h5-counter;
  counter-reset: h6-counter;
}

.ProseMirror .numbered-heading[data-level="6"] {
  counter-increment: h6-counter;
}

/* 显示编号 */
.ProseMirror .numbered-heading[data-level="1"]::before {
  content: counter(h1-counter) ". ";
  font-weight: inherit;
}

.ProseMirror .numbered-heading[data-level="2"]::before {
  content: counter(h1-counter) "." counter(h2-counter) ". ";
  font-weight: inherit;
}

.ProseMirror .numbered-heading[data-level="3"]::before {
  content: counter(h1-counter) "." counter(h2-counter) "." counter(h3-counter) ". ";
  font-weight: inherit;
}

.ProseMirror .numbered-heading[data-level="4"]::before {
  content: counter(h1-counter) "." counter(h2-counter) "." counter(h3-counter) "." counter(h4-counter) ". ";
  font-weight: inherit;
}

.ProseMirror .numbered-heading[data-level="5"]::before {
  content: counter(h1-counter) "." counter(h2-counter) "." counter(h3-counter) "." counter(h4-counter) "." counter(h5-counter) ". ";
  font-weight: inherit;
}

.ProseMirror .numbered-heading[data-level="6"]::before {
  content: counter(h1-counter) "." counter(h2-counter) "." counter(h3-counter) "." counter(h4-counter) "." counter(h5-counter) "." counter(h6-counter) ". ";
  font-weight: inherit;
}

/* Ensure proper cursor behavior in headings */
.ProseMirror h1,
.ProseMirror h2,
.ProseMirror h3,
.ProseMirror h4,
.ProseMirror h5,
.ProseMirror h6 {
  position: relative;
  display: block;
  line-height: 1.4;
  cursor: text;
}

/* Figure with Caption */
.ProseMirror figure[data-type="figure-with-caption"] {
  margin: 24px 0;
  text-align: center;
  background: #fafafa;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  padding: 16px;
}

.ProseMirror figure[data-type="figure-with-caption"] img {
  max-width: 100%;
  height: auto;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.ProseMirror figure[data-type="figure-with-caption"] figcaption {
  margin-top: 12px;
  font-size: 14px;
  color: #666;
  font-style: italic;
}

/* Equation Block */
.ProseMirror div[data-type="equation-block"] {
  margin: 24px 0;
  padding: 16px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-left: 4px solid #ee1d1d;
  border-radius: 4px;
  text-align: center;
  position: relative;
}

.ProseMirror div[data-type="equation-block"][data-number]:not([data-number=""]):before {
  content: "(" attr(data-number) ")";
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 14px;
  color: #666;
}

.ProseMirror .math-node {
  font-size: 1.1em;
}

/* Math Block - Updated for third-party extension */
.ProseMirror .math-block-container,
.ProseMirror .math-container {
  position: relative;
  margin: 24px 0;
}

.ProseMirror .math-block {
  padding: 16px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-left: 4px solid #ee1d1d;
  border-radius: 4px;
  text-align: center;
  min-height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Math Inline - For third-party extension */
.ProseMirror .math-inline-wrapper {
  display: inline-block;
  position: relative;
  margin: 0 2px;
}

.ProseMirror .math-inline {
  display: inline-block;
  padding: 2px 4px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 3px;
}

/* Third-party math extension styles */
.ProseMirror .math-node {
  position: relative;
}

.ProseMirror .math-node:hover .block-node-actions {
  opacity: 1;
  pointer-events: auto;
}

/* @aarkue/tiptap-math-extension specific styles */
.ProseMirror .math-inline-node {
  display: inline-block;
  position: relative;
  margin: 0 2px;
  padding: 2px 6px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
  font-size: 14px;
  transition: all 0.2s ease;
}

.ProseMirror .math-inline-node:hover {
  background: #e9ecef;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.ProseMirror .math-block-node {
  display: block;
  margin: 16px 0;
  padding: 16px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  text-align: center;
  position: relative;
  font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
  font-size: 14px;
  overflow-x: auto;
  transition: all 0.2s ease;
}

.ProseMirror .math-block-node:hover {
  background: #e9ecef;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 为第三方扩展的数学节点添加代码块风格 */
.ProseMirror .math-node,
.ProseMirror .math-container {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 16px;
  margin: 16px 0;
  position: relative;
  transition: all 0.2s ease;
}

.ProseMirror .math-node:hover,
.ProseMirror .math-container:hover {
  background: #e9ecef;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 第三方扩展的行内数学节点 */
.ProseMirror .math-inline-wrapper .math-container {
  display: inline-block;
  margin: 0 4px;
  padding: 4px 8px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  vertical-align: middle;
}

/* 第三方扩展的块级数学节点 */
.ProseMirror .math-block-wrapper .math-container {
  display: block;
  margin: 16px 0;
  padding: 16px;
  text-align: center;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
}

/* 确保 KaTeX 渲染的内容居中 */
.ProseMirror .math-block .katex-display {
  margin: 0 !important;
  text-align: center !important;
}

.ProseMirror .math-block .katex {
  font-size: 1.2em !important;
}

/* 第三方扩展的默认样式覆盖 */
.ProseMirror .math-inline {
  display: inline-block;
  padding: 4px 8px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  margin: 0 2px;
  font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
  transition: all 0.2s ease;
}

.ProseMirror .math-inline:hover {
  background: #e9ecef;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 为第三方扩展的块级数学公式添加样式 */
.ProseMirror .math-display {
  display: block;
  margin: 16px 0;
  padding: 16px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  text-align: center;
  font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
  transition: all 0.2s ease;
}

.ProseMirror .math-display:hover {
  background: #e9ecef;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 通用数学节点样式 */
.ProseMirror [data-type="math"] {
  position: relative;
}

.ProseMirror [data-type="math"]:hover .block-node-actions {
  opacity: 1;
  pointer-events: auto;
}

/* Theorem Block - Updated for NodeView */
.ProseMirror .theorem-block-container {
  position: relative;
  margin: 24px 0;
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  overflow: hidden;
  background: #fff;
}

.ProseMirror .theorem-block-container .theorem-header {
  background: linear-gradient(135deg, #ee1d1d, #ff4d4f);
  color: white;
  padding: 12px 16px;
  font-weight: 600;
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.ProseMirror .theorem-block-container .theorem-content {
  padding: 16px;
  background: #fafafa;
}

/* Enhanced Blockquote */
.ProseMirror blockquote[data-type="enhanced"] {
  margin: 24px 0;
  padding: 16px 20px;
  border-left: 4px solid #ee1d1d;
  background: #f8f9fa;
  border-radius: 0 8px 8px 0;
  font-style: italic;
  position: relative;
}

.ProseMirror blockquote[data-type="enhanced"]:before {
  content: "";
  font-size: 48px;
  color: #ee1d1d;
  position: absolute;
  top: -8px;
  left: 16px;
  opacity: 0.3;
}

.ProseMirror blockquote[data-type="enhanced"] cite {
  display: block;
  margin-top: 12px;
  font-size: 14px;
  color: #666;
  text-align: right;
  font-style: normal;
}

/* Lists */
.ProseMirror ul,
.ProseMirror ol {
  margin: 16px 0;
  padding-left: 24px;
}

.ProseMirror li {
  margin: 8px 0;
}

/* Links */
.ProseMirror .editor-link {
  color: #ee1d1d;
  text-decoration: none;
  border-bottom: 1px solid transparent;
  transition: all 0.2s ease;
}

.ProseMirror .editor-link:hover {
  border-bottom-color: #ee1d1d;
}

/* Images */
.ProseMirror .editor-image {
  max-width: 100%;
  height: auto;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin: 16px 0;
}

/* Code Block - Updated for NodeView */
.ProseMirror .code-block-container {
  position: relative;
  margin: 16px 0;
}

.ProseMirror .code-block {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 16px;
  font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
  font-size: 14px;
  line-height: 1.5;
  color: #333;
  overflow-x: auto;
  white-space: pre;
  margin: 0;
}

.ProseMirror .code-block code {
  background: none;
  padding: 0;
  border: none;
  font-family: inherit;
}

/* Tables */
.ProseMirror table {
  border-collapse: collapse;
  margin: 24px 0;
  width: 100%;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  overflow: hidden;
}

.ProseMirror th,
.ProseMirror td {
  border: 1px solid #e8e8e8;
  padding: 12px;
  text-align: left;
  vertical-align: top;
}

.ProseMirror th {
  background: #fafafa;
  font-weight: 600;
  color: #333;
}

.ProseMirror tr:nth-child(even) {
  background: #fafafa;
}

/* Bubble Menu */
.bubble-menu {
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  border: 1px solid #e8e8e8;
  padding: 8px;
  z-index: 1000;
}

.bubble-menu-content {
  display: flex;
  align-items: center;
  gap: 4px;
}

.bubble-menu .ant-btn {
  border: none;
  box-shadow: none;
}

.bubble-menu .ant-btn:hover {
  background: #f5f5f5;
}

.bubble-menu .ant-btn-primary {
  background: #ee1d1d;
  color: white;
}

.bubble-menu .ant-btn-primary:hover {
  background: #d91414;
}

/* Editor Status Bar */
.editor-status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 24px;
  background: #fafafa;
  border-top: 1px solid #e8e8e8;
  font-size: 12px;
  color: #666;
}

.editor-stats {
  display: flex;
  gap: 16px;
}

.editor-mode .ant-btn {
  font-size: 12px;
  height: 24px;
  padding: 0 8px;
}

/* Placeholder */
.ProseMirror p.is-editor-empty:first-child::before {
  content: attr(data-placeholder);
  float: left;
  color: #adb5bd;
  pointer-events: none;
  height: 0;
}

/* Focus styles */
.ProseMirror:focus {
  outline: none;
}

/* Selection styles */
.ProseMirror ::selection {
  background: rgba(238, 29, 29, 0.2);
}

/* Responsive design */
@media (max-width: 768px) {
  .editor-wrapper {
    padding: 16px;
  }

  .ProseMirror {
    font-size: 14px;
  }

  .bubble-menu-content {
    flex-wrap: wrap;
  }

  .editor-status-bar {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }
}
