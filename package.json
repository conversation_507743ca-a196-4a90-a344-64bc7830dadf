{"name": "scihub_engine", "version": "0.1.0", "private": true, "packageManager": "yarn@1.22.21+sha1.****************************************", "dependencies": {"@aarkue/tiptap-math-extension": "^1.3.6", "@ant-design/icons": "^5.6.0", "@clerk/clerk-react": "^5.33.0", "@irys/sdk": "^0.2.11", "@irys/upload": "^0.0.15", "@irys/upload-ethereum": "^0.0.16", "@irys/upload-solana": "^0.1.8", "@rainbow-me/rainbowkit": "^2.2.4", "@solana/wallet-adapter-react": "^0.15.35", "@solana/wallet-adapter-react-ui": "^0.9.35", "@solana/wallet-adapter-wallets": "^0.19.32", "@solana/web3.js": "1", "@tanstack/react-query": "^5.69.0", "@testing-library/jest-dom": "^5.14.1", "@testing-library/react": "^13.0.0", "@testing-library/user-event": "^13.2.1", "@tiptap/core": "^3.0.7", "@tiptap/extension-bubble-menu": "^3.0.7", "@tiptap/extension-character-count": "^3.0.7", "@tiptap/extension-floating-menu": "^3.0.7", "@tiptap/extension-image": "^3.0.7", "@tiptap/extension-link": "^3.0.7", "@tiptap/extension-mathematics": "^3.0.7", "@tiptap/extension-placeholder": "^3.0.7", "@tiptap/extension-table": "^3.0.7", "@tiptap/extension-table-cell": "^3.0.7", "@tiptap/extension-table-header": "^3.0.7", "@tiptap/extension-table-row": "^3.0.7", "@tiptap/extension-typography": "^3.0.7", "@tiptap/pm": "^3.0.7", "@tiptap/react": "^3.0.7", "@tiptap/starter-kit": "^3.0.7", "@tiptap/suggestion": "^3.0.7", "@types/react": "^19.0.8", "antd": "^5.23.3", "assert": "^2.1.0", "axios": "^1.9.0", "browserify-zlib": "^0.2.0", "buffer": "^6.0.3", "cors": "^2.8.5", "crypto-browserify": "^3.12.1", "dayjs": "^1.11.13", "dotenv": "^17.0.1", "express": "^4.21.2", "framer-motion": "^12.9.4", "html2canvas": "^1.4.1", "https-browserify": "^1.0.0", "install": "^0.13.0", "katex": "^0.16.22", "lz-string": "^1.5.0", "npm": "^11.1.0", "process": "^0.11.10", "prosemirror-state": "^1.4.3", "prosemirror-view": "^1.40.1", "react": "^18.0.0", "react-app-rewired": "^2.2.1", "react-dom": "^18.0.0", "react-katex": "^3.1.0", "react-markdown": "^9.0.3", "react-router-dom": "^7.1.5", "react-scripts": "5.0.1", "remark-gfm": "^4.0.1", "stream-browserify": "^3.0.0", "stream-http": "^3.2.0", "url": "^0.11.4", "viem": "2.x", "wagmi": "^2.14.15", "web-vitals": "^2.1.0"}, "devDependencies": {"assert": "^2.0.0", "buffer": "^6.0.3", "concurrently": "^9.1.0", "crypto-browserify": "^3.12.0", "https-browserify": "^1.0.0", "os-browserify": "^0.3.0", "process": "^0.11.10", "react-app-rewired": "^2.2.1", "stream-browserify": "^3.0.0", "stream-http": "^3.2.0", "url": "^0.11.0", "webpack-cli": "^6.0.1"}, "scripts": {"start": "concurrently \"yarn server\" \"yarn client\"", "client": "react-app-rewired start", "build": "react-app-rewired build", "test": "react-app-rewired test", "eject": "react-app-rewired eject", "server": "node server.js", "dev": "concurrently \"yarn server\" \"yarn client\""}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browser": {"crypto": false, "stream": false}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:3001"}